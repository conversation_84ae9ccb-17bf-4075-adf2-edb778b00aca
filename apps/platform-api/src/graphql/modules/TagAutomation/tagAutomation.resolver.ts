import { composeResolvers } from '@graphql-tools/resolvers-composition';
import IsAdmin from '../../../resolvers/composition/IsAdmin.js';
import type {
  GqlResolvers as Resolvers,
  GqlTagAutomationActionType,
  GqlTagAutomationTriggerType,
  GqlCreateTagAutomationInput,
  GqlUpdateTagAutomationInput,
  GqlDeleteTagAutomationInput,
} from '../../../graphql/__generated__/resolvers-types.js';
import type { TagAutomation } from '@bybeam/platform-types';

const tagAutomationResolvers: Resolvers = {
  Query: {
    tagAutomations: async (_root, _, context) => {
      return context.services.tagAutomations.findByPartnerId(context.token.partnerId);
    },
  },
  TagAutomation: {
    actionType: (tagAutomation: TagAutomation) => {
      // Convert platform-types enum to GraphQL enum
      return tagAutomation.actionType as unknown as GqlTagAutomationActionType;
    },
    triggerType: (tagAutomation: TagAutomation) => {
      // Convert platform-types enum to GraphQL enum
      return tagAutomation.triggerType as unknown as GqlTagAutomationTriggerType;
    },
  },
  Mutation: {
    TagAutomationsMutations: {
      create: async (_root, { input }, context) => {
        return context.services.tagAutomations.create(context.token, input);
      },
      update: async (_root, { input }, context) => {
        return context.services.tagAutomations.update(context.token, input);
      },
      delete: async (_root, { input }, context) => {
        return context.services.tagAutomations.delete(context.token, input);
      },
    },
  },
};

const resolverComposition = {
  'Query.tagAutomations': [IsAdmin()],
};

export default composeResolvers(tagAutomationResolvers, resolverComposition);
